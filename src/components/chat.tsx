'use client';

import { useChat } from 'ai/react';
import { Send, Bot, User } from 'lucide-react';
import { useRef, useEffect } from 'react';

export default function Chat() {
  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat();
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Chat Header */}
      <div className="border-b border-slate-200 bg-white/80 backdrop-blur-sm p-6 shadow-sm">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
            <Bot className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-slate-800">GenUI Assistant</h2>
            <p className="text-sm text-slate-600">Tell me what you want to build</p>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {messages.length === 0 && (
          <div className="text-center text-slate-500 mt-16">
            <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
              <Bot className="w-10 h-10 text-white" />
            </div>
            <h3 className="text-2xl font-bold text-slate-800 mb-2">Welcome to GenUI</h3>
            <p className="text-slate-600 max-w-md mx-auto leading-relaxed">
              Describe your web application idea and I'll generate the complete code for you.
              From simple landing pages to complex interactive apps!
            </p>
            <div className="mt-8 grid grid-cols-1 gap-3 max-w-sm mx-auto">
              <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 text-left border border-slate-200">
                <p className="text-sm text-slate-700">"Create a todo app with React"</p>
              </div>
              <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 text-left border border-slate-200">
                <p className="text-sm text-slate-700">"Build a landing page for a SaaS product"</p>
              </div>
              <div className="bg-white/60 backdrop-blur-sm rounded-lg p-3 text-left border border-slate-200">
                <p className="text-sm text-slate-700">"Make a weather dashboard"</p>
              </div>
            </div>
          </div>
        )}
        
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex items-start space-x-4 ${
              message.role === 'user' ? 'justify-end' : 'justify-start'
            }`}
          >
            {message.role === 'assistant' && (
              <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-md flex-shrink-0">
                <Bot className="w-5 h-5 text-white" />
              </div>
            )}

            <div
              className={`max-w-[75%] p-4 rounded-2xl shadow-sm ${
                message.role === 'user'
                  ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'
                  : 'bg-white/80 backdrop-blur-sm border border-slate-200 text-slate-800'
              }`}
            >
              <div className="whitespace-pre-wrap leading-relaxed">{message.content}</div>
            </div>

            {message.role === 'user' && (
              <div className="w-10 h-10 bg-gradient-to-r from-slate-500 to-slate-600 rounded-xl flex items-center justify-center shadow-md flex-shrink-0">
                <User className="w-5 h-5 text-white" />
              </div>
            )}
          </div>
        ))}
        
        {isLoading && (
          <div className="flex items-start space-x-4">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-md flex-shrink-0">
              <Bot className="w-5 h-5 text-white" />
            </div>
            <div className="bg-white/80 backdrop-blur-sm border border-slate-200 shadow-sm p-4 rounded-2xl">
              <div className="flex space-x-2">
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-blue-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input Form */}
      <div className="border-t border-slate-200 bg-white/80 backdrop-blur-sm p-6">
        <form onSubmit={handleSubmit} className="flex space-x-3">
          <div className="flex-1 relative">
            <input
              value={input}
              onChange={handleInputChange}
              placeholder="Describe what you want to build..."
              className="w-full p-4 pr-12 border border-slate-300 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 backdrop-blur-sm shadow-sm text-slate-800 placeholder-slate-500"
              disabled={isLoading}
            />
          </div>
          <button
            type="submit"
            disabled={isLoading || !input.trim()}
            className="px-6 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-2xl hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 shadow-lg transition-all duration-200 transform hover:scale-105 disabled:hover:scale-100"
          >
            <Send className="w-5 h-5" />
            <span className="font-medium">Send</span>
          </button>
        </form>
      </div>
    </div>
  );
}
