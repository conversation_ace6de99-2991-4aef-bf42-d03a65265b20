'use client';

import { useState } from 'react';
import { FileText, Folder, Play, Download } from 'lucide-react';

interface FileNode {
  name: string;
  type: 'file' | 'folder';
  content?: string;
  children?: FileNode[];
}

export default function CodeEditor() {
  const [files] = useState<FileNode[]>([
    {
      name: 'src',
      type: 'folder',
      children: [
        {
          name: 'App.tsx',
          type: 'file',
          content: `import React from 'react';

function App() {
  return (
    <div className="App">
      <h1>Hello World!</h1>
      <p>Your generated code will appear here.</p>
    </div>
  );
}

export default App;`
        },
        {
          name: 'index.css',
          type: 'file',
          content: `body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}`
        }
      ]
    },
    {
      name: 'package.json',
      type: 'file',
      content: `{
  "name": "generated-app",
  "version": "0.1.0",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build"
  }
}`
    }
  ]);

  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null);

  const renderFileTree = (nodes: FileNode[], depth = 0) => {
    return nodes.map((node, index) => (
      <div key={index} style={{ marginLeft: depth * 16 }}>
        <div
          className={`flex items-center space-x-2 p-2 hover:bg-gray-100 cursor-pointer rounded ${
            selectedFile === node ? 'bg-blue-100' : ''
          }`}
          onClick={() => node.type === 'file' && setSelectedFile(node)}
        >
          {node.type === 'folder' ? (
            <Folder className="w-4 h-4 text-blue-500" />
          ) : (
            <FileText className="w-4 h-4 text-gray-500" />
          )}
          <span className="text-sm">{node.name}</span>
        </div>
        {node.children && renderFileTree(node.children, depth + 1)}
      </div>
    ));
  };

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Editor Header */}
      <div className="border-b p-4 flex items-center justify-between">
        <h2 className="text-lg font-semibold text-gray-800">Code Editor</h2>
        <div className="flex space-x-2">
          <button className="flex items-center space-x-2 px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600">
            <Play className="w-4 h-4" />
            <span>Run</span>
          </button>
          <button className="flex items-center space-x-2 px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600">
            <Download className="w-4 h-4" />
            <span>Download</span>
          </button>
        </div>
      </div>

      <div className="flex flex-1">
        {/* File Explorer */}
        <div className="w-64 border-r bg-gray-50 p-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">Files</h3>
          <div className="space-y-1">
            {renderFileTree(files)}
          </div>
        </div>

        {/* Code Editor Area */}
        <div className="flex-1 flex flex-col">
          {selectedFile ? (
            <>
              <div className="border-b p-3 bg-gray-50">
                <span className="text-sm font-medium">{selectedFile.name}</span>
              </div>
              <div className="flex-1 p-4">
                <pre className="text-sm font-mono bg-gray-900 text-green-400 p-4 rounded overflow-auto h-full">
                  <code>{selectedFile.content}</code>
                </pre>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-gray-500">
              <div className="text-center">
                <FileText className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                <p>Select a file to view its content</p>
                <p className="text-sm mt-2">CodeBlitz editor will be integrated here</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
