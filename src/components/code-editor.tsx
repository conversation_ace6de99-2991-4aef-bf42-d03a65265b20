'use client';

import { useState } from 'react';
import { FileText, Folder, Play, Download } from 'lucide-react';

interface FileNode {
  name: string;
  type: 'file' | 'folder';
  content?: string;
  children?: FileNode[];
}

export default function CodeEditor() {
  const [files] = useState<FileNode[]>([
    {
      name: 'src',
      type: 'folder',
      children: [
        {
          name: 'App.tsx',
          type: 'file',
          content: `import React from 'react';

function App() {
  return (
    <div className="App">
      <h1>Hello World!</h1>
      <p>Your generated code will appear here.</p>
    </div>
  );
}

export default App;`
        },
        {
          name: 'index.css',
          type: 'file',
          content: `body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}`
        }
      ]
    },
    {
      name: 'package.json',
      type: 'file',
      content: `{
  "name": "generated-app",
  "version": "0.1.0",
  "private": true,
  "dependencies": {
    "react": "^18.2.0",
    "react-dom": "^18.2.0"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build"
  }
}`
    }
  ]);

  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null);

  const renderFileTree = (nodes: FileNode[], depth = 0) => {
    return nodes.map((node, index) => (
      <div key={index} style={{ marginLeft: depth * 16 }}>
        <div
          className={`flex items-center space-x-3 p-3 hover:bg-slate-700/50 cursor-pointer rounded-lg transition-all duration-200 ${
            selectedFile === node ? 'bg-slate-700 border border-slate-600' : ''
          }`}
          onClick={() => node.type === 'file' && setSelectedFile(node)}
        >
          {node.type === 'folder' ? (
            <Folder className="w-4 h-4 text-blue-400" />
          ) : (
            <FileText className="w-4 h-4 text-slate-400" />
          )}
          <span className="text-sm text-slate-300 font-medium">{node.name}</span>
        </div>
        {node.children && renderFileTree(node.children, depth + 1)}
      </div>
    ));
  };

  return (
    <div className="flex flex-col h-full bg-gradient-to-br from-slate-900 to-slate-800">
      {/* Editor Header */}
      <div className="border-b border-slate-700 bg-slate-800/90 backdrop-blur-sm p-6 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center">
            <FileText className="w-5 h-5 text-white" />
          </div>
          <div>
            <h2 className="text-xl font-bold text-white">Code Editor</h2>
            <p className="text-sm text-slate-400">Your generated code will appear here</p>
          </div>
        </div>
        <div className="flex space-x-3">
          <button className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 shadow-lg transition-all duration-200 transform hover:scale-105">
            <Play className="w-4 h-4" />
            <span className="font-medium">Run</span>
          </button>
          <button className="flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-slate-600 to-slate-700 text-white rounded-xl hover:from-slate-700 hover:to-slate-800 shadow-lg transition-all duration-200 transform hover:scale-105">
            <Download className="w-4 h-4" />
            <span className="font-medium">Download</span>
          </button>
        </div>
      </div>

      <div className="flex flex-1">
        {/* File Explorer */}
        <div className="w-72 border-r border-slate-700 bg-slate-800/50 backdrop-blur-sm p-6">
          <h3 className="text-sm font-semibold text-slate-300 mb-4 uppercase tracking-wider">Project Files</h3>
          <div className="space-y-1">
            {renderFileTree(files)}
          </div>
        </div>

        {/* Code Editor Area */}
        <div className="flex-1 flex flex-col">
          {selectedFile ? (
            <>
              <div className="border-b border-slate-700 p-4 bg-slate-800/30 backdrop-blur-sm">
                <div className="flex items-center space-x-2">
                  <FileText className="w-4 h-4 text-slate-400" />
                  <span className="text-sm font-medium text-slate-300">{selectedFile.name}</span>
                </div>
              </div>
              <div className="flex-1 p-6">
                <div className="bg-slate-900/80 backdrop-blur-sm rounded-xl border border-slate-700 h-full overflow-hidden">
                  <pre className="text-sm font-mono text-slate-300 p-6 overflow-auto h-full leading-relaxed">
                    <code className="text-emerald-400">{selectedFile.content}</code>
                  </pre>
                </div>
              </div>
            </>
          ) : (
            <div className="flex-1 flex items-center justify-center text-slate-400">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-slate-700 to-slate-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg">
                  <FileText className="w-10 h-10 text-slate-300" />
                </div>
                <h3 className="text-xl font-bold text-slate-300 mb-2">Ready to Code</h3>
                <p className="text-slate-500 mb-2">Select a file to view its content</p>
                <p className="text-sm text-slate-600">CodeBlitz editor will be integrated here</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
