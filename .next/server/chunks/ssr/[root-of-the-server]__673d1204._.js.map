{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/genui/src/components/chat.tsx"], "sourcesContent": ["'use client';\n\nimport { useChat } from 'ai/react';\nimport { Send, Bot, User } from 'lucide-react';\nimport { useRef, useEffect } from 'react';\n\nexport default function Chat() {\n  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat();\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  return (\n    <div className=\"flex flex-col h-full bg-gray-50\">\n      {/* Chat Header */}\n      <div className=\"border-b bg-white p-4\">\n        <h2 className=\"text-lg font-semibold text-gray-800\">AI Assistant</h2>\n        <p className=\"text-sm text-gray-600\">Describe what you want to build</p>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n        {messages.length === 0 && (\n          <div className=\"text-center text-gray-500 mt-8\">\n            <Bot className=\"w-12 h-12 mx-auto mb-4 text-gray-400\" />\n            <p className=\"text-lg font-medium\">Welcome to GenUI</p>\n            <p className=\"text-sm\">Tell me what you want to build and I'll help you create it!</p>\n          </div>\n        )}\n        \n        {messages.map((message) => (\n          <div\n            key={message.id}\n            className={`flex items-start space-x-3 ${\n              message.role === 'user' ? 'justify-end' : 'justify-start'\n            }`}\n          >\n            {message.role === 'assistant' && (\n              <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\n                <Bot className=\"w-4 h-4 text-white\" />\n              </div>\n            )}\n            \n            <div\n              className={`max-w-[70%] p-3 rounded-lg ${\n                message.role === 'user'\n                  ? 'bg-blue-500 text-white'\n                  : 'bg-white border shadow-sm'\n              }`}\n            >\n              <div className=\"whitespace-pre-wrap\">{message.content}</div>\n            </div>\n            \n            {message.role === 'user' && (\n              <div className=\"w-8 h-8 bg-gray-500 rounded-full flex items-center justify-center\">\n                <User className=\"w-4 h-4 text-white\" />\n              </div>\n            )}\n          </div>\n        ))}\n        \n        {isLoading && (\n          <div className=\"flex items-start space-x-3\">\n            <div className=\"w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center\">\n              <Bot className=\"w-4 h-4 text-white\" />\n            </div>\n            <div className=\"bg-white border shadow-sm p-3 rounded-lg\">\n              <div className=\"flex space-x-1\">\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n              </div>\n            </div>\n          </div>\n        )}\n        \n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input Form */}\n      <div className=\"border-t bg-white p-4\">\n        <form onSubmit={handleSubmit} className=\"flex space-x-2\">\n          <input\n            value={input}\n            onChange={handleInputChange}\n            placeholder=\"Describe what you want to build...\"\n            className=\"flex-1 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n            disabled={isLoading}\n          />\n          <button\n            type=\"submit\"\n            disabled={isLoading || !input.trim()}\n            className=\"px-4 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2\"\n          >\n            <Send className=\"w-4 h-4\" />\n          </button>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,iBAAiB,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD;IAC9E,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAIvC,8OAAC;gBAAI,WAAU;;oBACZ,SAAS,MAAM,KAAK,mBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAsB;;;;;;0CACnC,8OAAC;gCAAE,WAAU;0CAAU;;;;;;;;;;;;oBAI1B,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4BAEC,WAAW,CAAC,2BAA2B,EACrC,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAC1C;;gCAED,QAAQ,IAAI,KAAK,6BAChB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAInB,8OAAC;oCACC,WAAW,CAAC,2BAA2B,EACrC,QAAQ,IAAI,KAAK,SACb,2BACA,6BACJ;8CAEF,cAAA,8OAAC;wCAAI,WAAU;kDAAuB,QAAQ,OAAO;;;;;;;;;;;gCAGtD,QAAQ,IAAI,KAAK,wBAChB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;2BAvBf,QAAQ,EAAE;;;;;oBA6BlB,2BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;sDACjG,8OAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;;;;;;;;;;;;;;;;;;kCAMzG,8OAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BACC,OAAO;4BACP,UAAU;4BACV,aAAY;4BACZ,WAAU;4BACV,UAAU;;;;;;sCAEZ,8OAAC;4BACC,MAAK;4BACL,UAAU,aAAa,CAAC,MAAM,IAAI;4BAClC,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5B", "debugId": null}}, {"offset": {"line": 293, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/genui/src/components/code-editor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { FileText, Folder, Play, Download } from 'lucide-react';\n\ninterface FileNode {\n  name: string;\n  type: 'file' | 'folder';\n  content?: string;\n  children?: FileNode[];\n}\n\nexport default function CodeEditor() {\n  const [files] = useState<FileNode[]>([\n    {\n      name: 'src',\n      type: 'folder',\n      children: [\n        {\n          name: 'App.tsx',\n          type: 'file',\n          content: `import React from 'react';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <h1>Hello World!</h1>\n      <p>Your generated code will appear here.</p>\n    </div>\n  );\n}\n\nexport default App;`\n        },\n        {\n          name: 'index.css',\n          type: 'file',\n          content: `body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}`\n        }\n      ]\n    },\n    {\n      name: 'package.json',\n      type: 'file',\n      content: `{\n  \"name\": \"generated-app\",\n  \"version\": \"0.1.0\",\n  \"private\": true,\n  \"dependencies\": {\n    \"react\": \"^18.2.0\",\n    \"react-dom\": \"^18.2.0\"\n  },\n  \"scripts\": {\n    \"start\": \"react-scripts start\",\n    \"build\": \"react-scripts build\"\n  }\n}`\n    }\n  ]);\n\n  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null);\n\n  const renderFileTree = (nodes: FileNode[], depth = 0) => {\n    return nodes.map((node, index) => (\n      <div key={index} style={{ marginLeft: depth * 16 }}>\n        <div\n          className={`flex items-center space-x-2 p-2 hover:bg-gray-100 cursor-pointer rounded ${\n            selectedFile === node ? 'bg-blue-100' : ''\n          }`}\n          onClick={() => node.type === 'file' && setSelectedFile(node)}\n        >\n          {node.type === 'folder' ? (\n            <Folder className=\"w-4 h-4 text-blue-500\" />\n          ) : (\n            <FileText className=\"w-4 h-4 text-gray-500\" />\n          )}\n          <span className=\"text-sm\">{node.name}</span>\n        </div>\n        {node.children && renderFileTree(node.children, depth + 1)}\n      </div>\n    ));\n  };\n\n  return (\n    <div className=\"flex flex-col h-full bg-white\">\n      {/* Editor Header */}\n      <div className=\"border-b p-4 flex items-center justify-between\">\n        <h2 className=\"text-lg font-semibold text-gray-800\">Code Editor</h2>\n        <div className=\"flex space-x-2\">\n          <button className=\"flex items-center space-x-2 px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600\">\n            <Play className=\"w-4 h-4\" />\n            <span>Run</span>\n          </button>\n          <button className=\"flex items-center space-x-2 px-3 py-2 bg-gray-500 text-white rounded hover:bg-gray-600\">\n            <Download className=\"w-4 h-4\" />\n            <span>Download</span>\n          </button>\n        </div>\n      </div>\n\n      <div className=\"flex flex-1\">\n        {/* File Explorer */}\n        <div className=\"w-64 border-r bg-gray-50 p-4\">\n          <h3 className=\"text-sm font-medium text-gray-700 mb-3\">Files</h3>\n          <div className=\"space-y-1\">\n            {renderFileTree(files)}\n          </div>\n        </div>\n\n        {/* Code Editor Area */}\n        <div className=\"flex-1 flex flex-col\">\n          {selectedFile ? (\n            <>\n              <div className=\"border-b p-3 bg-gray-50\">\n                <span className=\"text-sm font-medium\">{selectedFile.name}</span>\n              </div>\n              <div className=\"flex-1 p-4\">\n                <pre className=\"text-sm font-mono bg-gray-900 text-green-400 p-4 rounded overflow-auto h-full\">\n                  <code>{selectedFile.content}</code>\n                </pre>\n              </div>\n            </>\n          ) : (\n            <div className=\"flex-1 flex items-center justify-center text-gray-500\">\n              <div className=\"text-center\">\n                <FileText className=\"w-12 h-12 mx-auto mb-4 text-gray-400\" />\n                <p>Select a file to view its content</p>\n                <p className=\"text-sm mt-2\">CodeBlitz editor will be integrated here</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAYe,SAAS;IACtB,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QACnC;YACE,MAAM;YACN,MAAM;YACN,UAAU;gBACR;oBACE,MAAM;oBACN,MAAM;oBACN,SAAS,CAAC;;;;;;;;;;;mBAWD,CAAC;gBACZ;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,SAAS,CAAC;;;;;;;CAOnB,CAAC;gBACM;aACD;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS,CAAC;;;;;;;;;;;;CAYf,CAAC;QACE;KACD;IAED,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAElE,MAAM,iBAAiB,CAAC,OAAmB,QAAQ,CAAC;QAClD,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;gBAAgB,OAAO;oBAAE,YAAY,QAAQ;gBAAG;;kCAC/C,8OAAC;wBACC,WAAW,CAAC,yEAAyE,EACnF,iBAAiB,OAAO,gBAAgB,IACxC;wBACF,SAAS,IAAM,KAAK,IAAI,KAAK,UAAU,gBAAgB;;4BAEtD,KAAK,IAAI,KAAK,yBACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;qDAElB,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CAEtB,8OAAC;gCAAK,WAAU;0CAAW,KAAK,IAAI;;;;;;;;;;;;oBAErC,KAAK,QAAQ,IAAI,eAAe,KAAK,QAAQ,EAAE,QAAQ;;eAdhD;;;;;IAiBd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAKZ,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAI,WAAU;0CACZ,eAAe;;;;;;;;;;;;kCAKpB,8OAAC;wBAAI,WAAU;kCACZ,6BACC;;8CACE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAAuB,aAAa,IAAI;;;;;;;;;;;8CAE1D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;sDAAM,aAAa,OAAO;;;;;;;;;;;;;;;;;yDAKjC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,8MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;kDAAE;;;;;;kDACH,8OAAC;wCAAE,WAAU;kDAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5C", "debugId": null}}]}