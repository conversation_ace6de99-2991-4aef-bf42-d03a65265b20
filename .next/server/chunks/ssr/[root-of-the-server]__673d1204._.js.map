{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/genui/src/components/chat.tsx"], "sourcesContent": ["'use client';\n\nimport { useChat } from 'ai/react';\nimport { Send, Bot, User } from 'lucide-react';\nimport { useRef, useEffect } from 'react';\n\nexport default function Chat() {\n  const { messages, input, handleInputChange, handleSubmit, isLoading } = useChat();\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  return (\n    <div className=\"flex flex-col h-full bg-gradient-to-br from-slate-50 to-blue-50\">\n      {/* Chat Header */}\n      <div className=\"border-b border-slate-200 bg-white/80 backdrop-blur-sm p-6 shadow-sm\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center\">\n            <Bot className=\"w-5 h-5 text-white\" />\n          </div>\n          <div>\n            <h2 className=\"text-xl font-bold text-slate-800\">GenUI Assistant</h2>\n            <p className=\"text-sm text-slate-600\">Tell me what you want to build</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Messages */}\n      <div className=\"flex-1 overflow-y-auto p-6 space-y-6\">\n        {messages.length === 0 && (\n          <div className=\"text-center text-slate-500 mt-16\">\n            <div className=\"w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg\">\n              <Bot className=\"w-10 h-10 text-white\" />\n            </div>\n            <h3 className=\"text-2xl font-bold text-slate-800 mb-2\">Welcome to GenUI</h3>\n            <p className=\"text-slate-600 max-w-md mx-auto leading-relaxed\">\n              Describe your web application idea and I'll generate the complete code for you.\n              From simple landing pages to complex interactive apps!\n            </p>\n            <div className=\"mt-8 grid grid-cols-1 gap-3 max-w-sm mx-auto\">\n              <div className=\"bg-white/60 backdrop-blur-sm rounded-lg p-3 text-left border border-slate-200\">\n                <p className=\"text-sm text-slate-700\">\"Create a todo app with React\"</p>\n              </div>\n              <div className=\"bg-white/60 backdrop-blur-sm rounded-lg p-3 text-left border border-slate-200\">\n                <p className=\"text-sm text-slate-700\">\"Build a landing page for a SaaS product\"</p>\n              </div>\n              <div className=\"bg-white/60 backdrop-blur-sm rounded-lg p-3 text-left border border-slate-200\">\n                <p className=\"text-sm text-slate-700\">\"Make a weather dashboard\"</p>\n              </div>\n            </div>\n          </div>\n        )}\n        \n        {messages.map((message) => (\n          <div\n            key={message.id}\n            className={`flex items-start space-x-4 ${\n              message.role === 'user' ? 'justify-end' : 'justify-start'\n            }`}\n          >\n            {message.role === 'assistant' && (\n              <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-md flex-shrink-0\">\n                <Bot className=\"w-5 h-5 text-white\" />\n              </div>\n            )}\n\n            <div\n              className={`max-w-[75%] p-4 rounded-2xl shadow-sm ${\n                message.role === 'user'\n                  ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white'\n                  : 'bg-white/80 backdrop-blur-sm border border-slate-200 text-slate-800'\n              }`}\n            >\n              <div className=\"whitespace-pre-wrap leading-relaxed\">{message.content}</div>\n            </div>\n\n            {message.role === 'user' && (\n              <div className=\"w-10 h-10 bg-gradient-to-r from-slate-500 to-slate-600 rounded-xl flex items-center justify-center shadow-md flex-shrink-0\">\n                <User className=\"w-5 h-5 text-white\" />\n              </div>\n            )}\n          </div>\n        ))}\n        \n        {isLoading && (\n          <div className=\"flex items-start space-x-4\">\n            <div className=\"w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-md flex-shrink-0\">\n              <Bot className=\"w-5 h-5 text-white\" />\n            </div>\n            <div className=\"bg-white/80 backdrop-blur-sm border border-slate-200 shadow-sm p-4 rounded-2xl\">\n              <div className=\"flex space-x-2\">\n                <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-bounce\"></div>\n                <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                <div className=\"w-2 h-2 bg-blue-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n              </div>\n            </div>\n          </div>\n        )}\n        \n        <div ref={messagesEndRef} />\n      </div>\n\n      {/* Input Form */}\n      <div className=\"border-t border-slate-200 bg-white/80 backdrop-blur-sm p-6\">\n        <form onSubmit={handleSubmit} className=\"flex space-x-3\">\n          <div className=\"flex-1 relative\">\n            <input\n              value={input}\n              onChange={handleInputChange}\n              placeholder=\"Describe what you want to build...\"\n              className=\"w-full p-4 pr-12 border border-slate-300 rounded-2xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white/90 backdrop-blur-sm shadow-sm text-slate-800 placeholder-slate-500\"\n              disabled={isLoading}\n            />\n          </div>\n          <button\n            type=\"submit\"\n            disabled={isLoading || !input.trim()}\n            className=\"px-6 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-2xl hover:from-blue-600 hover:to-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2 shadow-lg transition-all duration-200 transform hover:scale-105 disabled:hover:scale-100\"\n          >\n            <Send className=\"w-5 h-5\" />\n            <span className=\"font-medium\">Send</span>\n          </button>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAJA;;;;;AAMe,SAAS;IACtB,MAAM,EAAE,QAAQ,EAAE,KAAK,EAAE,iBAAiB,EAAE,YAAY,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD;IAC9E,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAS;IAEb,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;;;;;;sCAEjB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,8OAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;;;;;;0BAM5C,8OAAC;gBAAI,WAAU;;oBACZ,SAAS,MAAM,KAAK,mBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEjB,8OAAC;gCAAG,WAAU;0CAAyC;;;;;;0CACvD,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;0CAI/D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;kDAExC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;sDAAyB;;;;;;;;;;;;;;;;;;;;;;;oBAM7C,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;4BAEC,WAAW,CAAC,2BAA2B,EACrC,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAC1C;;gCAED,QAAQ,IAAI,KAAK,6BAChB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAInB,8OAAC;oCACC,WAAW,CAAC,sCAAsC,EAChD,QAAQ,IAAI,KAAK,SACb,0DACA,uEACJ;8CAEF,cAAA,8OAAC;wCAAI,WAAU;kDAAuC,QAAQ,OAAO;;;;;;;;;;;gCAGtE,QAAQ,IAAI,KAAK,wBAChB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;2BAvBf,QAAQ,EAAE;;;;;oBA6BlB,2BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAEjB,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;sDACjG,8OAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;;;;;;;;;;;;;;;;;;kCAMzG,8OAAC;wBAAI,KAAK;;;;;;;;;;;;0BAIZ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,OAAO;gCACP,UAAU;gCACV,aAAY;gCACZ,WAAU;gCACV,UAAU;;;;;;;;;;;sCAGd,8OAAC;4BACC,MAAK;4BACL,UAAU,aAAa,CAAC,MAAM,IAAI;4BAClC,WAAU;;8CAEV,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM1C", "debugId": null}}, {"offset": {"line": 400, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/genui/src/components/code-editor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { FileText, Folder, Play, Download } from 'lucide-react';\n\ninterface FileNode {\n  name: string;\n  type: 'file' | 'folder';\n  content?: string;\n  children?: FileNode[];\n}\n\nexport default function CodeEditor() {\n  const [files] = useState<FileNode[]>([\n    {\n      name: 'src',\n      type: 'folder',\n      children: [\n        {\n          name: 'App.tsx',\n          type: 'file',\n          content: `import React from 'react';\n\nfunction App() {\n  return (\n    <div className=\"App\">\n      <h1>Hello World!</h1>\n      <p>Your generated code will appear here.</p>\n    </div>\n  );\n}\n\nexport default App;`\n        },\n        {\n          name: 'index.css',\n          type: 'file',\n          content: `body {\n  margin: 0;\n  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',\n    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',\n    sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n}`\n        }\n      ]\n    },\n    {\n      name: 'package.json',\n      type: 'file',\n      content: `{\n  \"name\": \"generated-app\",\n  \"version\": \"0.1.0\",\n  \"private\": true,\n  \"dependencies\": {\n    \"react\": \"^18.2.0\",\n    \"react-dom\": \"^18.2.0\"\n  },\n  \"scripts\": {\n    \"start\": \"react-scripts start\",\n    \"build\": \"react-scripts build\"\n  }\n}`\n    }\n  ]);\n\n  const [selectedFile, setSelectedFile] = useState<FileNode | null>(null);\n\n  const renderFileTree = (nodes: FileNode[], depth = 0) => {\n    return nodes.map((node, index) => (\n      <div key={index} style={{ marginLeft: depth * 16 }}>\n        <div\n          className={`flex items-center space-x-3 p-3 hover:bg-slate-700/50 cursor-pointer rounded-lg transition-all duration-200 ${\n            selectedFile === node ? 'bg-slate-700 border border-slate-600' : ''\n          }`}\n          onClick={() => node.type === 'file' && setSelectedFile(node)}\n        >\n          {node.type === 'folder' ? (\n            <Folder className=\"w-4 h-4 text-blue-400\" />\n          ) : (\n            <FileText className=\"w-4 h-4 text-slate-400\" />\n          )}\n          <span className=\"text-sm text-slate-300 font-medium\">{node.name}</span>\n        </div>\n        {node.children && renderFileTree(node.children, depth + 1)}\n      </div>\n    ));\n  };\n\n  return (\n    <div className=\"flex flex-col h-full bg-gradient-to-br from-slate-900 to-slate-800\">\n      {/* Editor Header */}\n      <div className=\"border-b border-slate-700 bg-slate-800/90 backdrop-blur-sm p-6 flex items-center justify-between\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-10 h-10 bg-gradient-to-r from-green-500 to-emerald-600 rounded-xl flex items-center justify-center\">\n            <FileText className=\"w-5 h-5 text-white\" />\n          </div>\n          <div>\n            <h2 className=\"text-xl font-bold text-white\">Code Editor</h2>\n            <p className=\"text-sm text-slate-400\">Your generated code will appear here</p>\n          </div>\n        </div>\n        <div className=\"flex space-x-3\">\n          <button className=\"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl hover:from-green-600 hover:to-green-700 shadow-lg transition-all duration-200 transform hover:scale-105\">\n            <Play className=\"w-4 h-4\" />\n            <span className=\"font-medium\">Run</span>\n          </button>\n          <button className=\"flex items-center space-x-2 px-4 py-2 bg-gradient-to-r from-slate-600 to-slate-700 text-white rounded-xl hover:from-slate-700 hover:to-slate-800 shadow-lg transition-all duration-200 transform hover:scale-105\">\n            <Download className=\"w-4 h-4\" />\n            <span className=\"font-medium\">Download</span>\n          </button>\n        </div>\n      </div>\n\n      <div className=\"flex flex-1\">\n        {/* File Explorer */}\n        <div className=\"w-72 border-r border-slate-700 bg-slate-800/50 backdrop-blur-sm p-6\">\n          <h3 className=\"text-sm font-semibold text-slate-300 mb-4 uppercase tracking-wider\">Project Files</h3>\n          <div className=\"space-y-1\">\n            {renderFileTree(files)}\n          </div>\n        </div>\n\n        {/* Code Editor Area */}\n        <div className=\"flex-1 flex flex-col\">\n          {selectedFile ? (\n            <>\n              <div className=\"border-b border-slate-700 p-4 bg-slate-800/30 backdrop-blur-sm\">\n                <div className=\"flex items-center space-x-2\">\n                  <FileText className=\"w-4 h-4 text-slate-400\" />\n                  <span className=\"text-sm font-medium text-slate-300\">{selectedFile.name}</span>\n                </div>\n              </div>\n              <div className=\"flex-1 p-6\">\n                <div className=\"bg-slate-900/80 backdrop-blur-sm rounded-xl border border-slate-700 h-full overflow-hidden\">\n                  <pre className=\"text-sm font-mono text-slate-300 p-6 overflow-auto h-full leading-relaxed\">\n                    <code className=\"text-emerald-400\">{selectedFile.content}</code>\n                  </pre>\n                </div>\n              </div>\n            </>\n          ) : (\n            <div className=\"flex-1 flex items-center justify-center text-slate-400\">\n              <div className=\"text-center\">\n                <div className=\"w-20 h-20 bg-gradient-to-r from-slate-700 to-slate-600 rounded-2xl flex items-center justify-center mx-auto mb-6 shadow-lg\">\n                  <FileText className=\"w-10 h-10 text-slate-300\" />\n                </div>\n                <h3 className=\"text-xl font-bold text-slate-300 mb-2\">Ready to Code</h3>\n                <p className=\"text-slate-500 mb-2\">Select a file to view its content</p>\n                <p className=\"text-sm text-slate-600\">CodeBlitz editor will be integrated here</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAHA;;;;AAYe,SAAS;IACtB,MAAM,CAAC,MAAM,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc;QACnC;YACE,MAAM;YACN,MAAM;YACN,UAAU;gBACR;oBACE,MAAM;oBACN,MAAM;oBACN,SAAS,CAAC;;;;;;;;;;;mBAWD,CAAC;gBACZ;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,SAAS,CAAC;;;;;;;CAOnB,CAAC;gBACM;aACD;QACH;QACA;YACE,MAAM;YACN,MAAM;YACN,SAAS,CAAC;;;;;;;;;;;;CAYf,CAAC;QACE;KACD;IAED,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAElE,MAAM,iBAAiB,CAAC,OAAmB,QAAQ,CAAC;QAClD,OAAO,MAAM,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC;gBAAgB,OAAO;oBAAE,YAAY,QAAQ;gBAAG;;kCAC/C,8OAAC;wBACC,WAAW,CAAC,4GAA4G,EACtH,iBAAiB,OAAO,yCAAyC,IACjE;wBACF,SAAS,IAAM,KAAK,IAAI,KAAK,UAAU,gBAAgB;;4BAEtD,KAAK,IAAI,KAAK,yBACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;qDAElB,8OAAC,8MAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CAEtB,8OAAC;gCAAK,WAAU;0CAAsC,KAAK,IAAI;;;;;;;;;;;;oBAEhE,KAAK,QAAQ,IAAI,eAAe,KAAK,QAAQ,EAAE,QAAQ;;eAdhD;;;;;IAiBd;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;0CAEtB,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA+B;;;;;;kDAC7C,8OAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;kCAG1C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;0CAEhC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,0MAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;kDAAc;;;;;;;;;;;;;;;;;;;;;;;;0BAKpC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqE;;;;;;0CACnF,8OAAC;gCAAI,WAAU;0CACZ,eAAe;;;;;;;;;;;;kCAKpB,8OAAC;wBAAI,WAAU;kCACZ,6BACC;;8CACE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,8MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAAsC,aAAa,IAAI;;;;;;;;;;;;;;;;;8CAG3E,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAAoB,aAAa,OAAO;;;;;;;;;;;;;;;;;;;;;;yDAMhE,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,8MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAAsB;;;;;;kDACnC,8OAAC;wCAAE,WAAU;kDAAyB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtD", "debugId": null}}]}