{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/genui/src/app/api/chat/route.ts"], "sourcesContent": ["import { openai } from '@ai-sdk/openai';\nimport { anthropic } from '@ai-sdk/anthropic';\nimport { streamText } from 'ai';\n\nexport async function POST(req: Request) {\n  const { messages } = await req.json();\n\n  // Choose AI provider based on environment variable\n  const provider = process.env.AI_PROVIDER || 'openai';\n  \n  let model;\n  if (provider === 'anthropic') {\n    model = anthropic('claude-3-sonnet-20240229');\n  } else {\n    model = openai('gpt-4-turbo-preview');\n  }\n\n  const result = await streamText({\n    model,\n    messages,\n    system: `You are an AI coding assistant similar to bolt.new. You help users create web applications by:\n1. Understanding their requirements\n2. Generating complete, working code\n3. Providing step-by-step explanations\n4. Suggesting improvements and best practices\n\nWhen generating code, always provide:\n- Complete, runnable code files\n- Clear file structure\n- Proper imports and dependencies\n- Comments explaining key functionality\n\nFocus on modern web technologies like React, Next.js, TypeScript, and Tailwind CSS.`,\n  });\n\n  return result.toDataStreamResponse();\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,GAAY;IACrC,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,IAAI,IAAI;IAEnC,mDAAmD;IACnD,MAAM,WAAW,QAAQ,GAAG,CAAC,WAAW,IAAI;IAE5C,IAAI;IACJ,IAAI,aAAa,aAAa;QAC5B,QAAQ,CAAA,GAAA,4JAAA,CAAA,YAAS,AAAD,EAAE;IACpB,OAAO;QACL,QAAQ,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE;IACjB;IAEA,MAAM,SAAS,MAAM,CAAA,GAAA,sJAAA,CAAA,aAAU,AAAD,EAAE;QAC9B;QACA;QACA,QAAQ,CAAC;;;;;;;;;;;;mFAYsE,CAAC;IAClF;IAEA,OAAO,OAAO,oBAAoB;AACpC", "debugId": null}}]}